package middlewares

import (
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
)

func TestHeaderToContextMiddleware(t *testing.T) {
	logger := zaptest.NewLogger(t)

	t.Run("ID valid header", func(t *testing.T) {
		middleware := HeaderToContextMiddleware("X-Service-Id", SvcIDKey{}, appioid.Parse, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			serviceID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
			assert.True(t, ok)
			assert.Equal(t, "svc_00000000000000000000000000", serviceID.String())
			w.WriteHeader(http.StatusOK)
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", "svc_00000000000000000000000000")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("ID invalid header returns 400", func(t *testing.T) {
		middleware := HeaderToContextMiddleware("X-Service-Id", SvcIDKey{}, appioid.Parse, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Error("Handler should not be called when middleware returns 400")
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", "invalid-id")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid X-Service-Id header")
	})

	t.Run("ID  missing header does not set context", func(t *testing.T) {
		middleware := HeaderToContextMiddleware("X-Service-Id", SvcIDKey{}, appioid.Parse, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			_, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
			assert.False(t, ok) // Should not be set
			w.WriteHeader(http.StatusOK)
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		// No X-Service-Id header
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Custom type with string parser", func(t *testing.T) {
		// Test with a simple string parser
		stringParser := func(s string) (string, error) {
			if s == "" {
				return "", assert.AnError
			}
			return s, nil
		}

		middleware := HeaderToContextMiddleware("X-Custom-Header", "custom-key", stringParser, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			value, ok := ctx.Value("custom-key").(string)
			assert.True(t, ok)
			assert.Equal(t, "test-value", value)
			w.WriteHeader(http.StatusOK)
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Custom-Header", "test-value")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Custom type with int parser - returns 400", func(t *testing.T) {
		// Test with an int parser
		intParser := func(s string) (int, error) {
			return strconv.Atoi(s)
		}

		middleware := HeaderToContextMiddleware("X-Number", "number-key", intParser, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Error("Handler should not be called when middleware returns 400")
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Number", "not-a-number")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid X-Number header")
	})
}
