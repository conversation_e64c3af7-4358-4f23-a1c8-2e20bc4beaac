package device_names

import (
	"testing"
	"unicode/utf16"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAndroidParser_ParseData(t *testing.T) {
	parser := NewAndroidParser()

	t.Run("valid CSV data", func(t *testing.T) {
		csvData := `Retail Branding,Marketing Name,Device,Model
Samsung,Galaxy S21,SM-G991B,SM-G991B
Google,Pixel 6,oriole,Pixel 6
OnePlus,OnePlus 9,LE2110,LE2110`

		deviceNames, err := parser.ParseData([]byte(csvData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 3)

		// Check first device
		assert.Equal(t, "Samsung", deviceNames[0].Brand)
		assert.Equal(t, "SM-G991B", deviceNames[0].Model)
		assert.Equal(t, "Galaxy S21", deviceNames[0].Name)

		// Check second device
		assert.Equal(t, "Google", deviceNames[1].Brand)
		assert.Equal(t, "Pixel 6", deviceNames[1].Model)
		assert.Equal(t, "Pixel 6", deviceNames[1].Name)
	})

	t.Run("empty CSV", func(t *testing.T) {
		csvData := ``

		deviceNames, err := parser.ParseData([]byte(csvData))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "CSV is empty")
		assert.Nil(t, deviceNames)
	})

	t.Run("missing required columns", func(t *testing.T) {
		csvData := `Brand,Name,Device
Samsung,Galaxy S21,SM-G991B`

		deviceNames, err := parser.ParseData([]byte(csvData))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "missing required column")
		assert.Nil(t, deviceNames)
	})

	t.Run("records with missing fields", func(t *testing.T) {
		csvData := `Retail Branding,Marketing Name,Device,Model
Samsung,Galaxy S21,SM-G991B,SM-G991B
,,SM-G991C,SM-G991C
Google,Pixel 6,oriole,
OnePlus,OnePlus 9,LE2110,LE2110`

		deviceNames, err := parser.ParseData([]byte(csvData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 2) // Only complete records should be included

		// Check that only valid records are included
		assert.Equal(t, "Samsung", deviceNames[0].Brand)
		assert.Equal(t, "OnePlus", deviceNames[1].Brand)
	})

	t.Run("case insensitive column headers", func(t *testing.T) {
		csvData := `retail branding,marketing name,device,model
Samsung,Galaxy S21,SM-G991B,SM-G991B`

		deviceNames, err := parser.ParseData([]byte(csvData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 1)

		assert.Equal(t, "Samsung", deviceNames[0].Brand)
		assert.Equal(t, "SM-G991B", deviceNames[0].Model)
		assert.Equal(t, "Galaxy S21", deviceNames[0].Name)
	})

	t.Run("records with insufficient fields", func(t *testing.T) {
		csvData := `Retail Branding,Marketing Name,Device,Model
Samsung,Galaxy S21
Google,Pixel 6,oriole,Pixel 6`

		deviceNames, err := parser.ParseData([]byte(csvData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 1) // Only the complete record should be included

		assert.Equal(t, "Google", deviceNames[0].Brand)
	})

	t.Run("CSV with bare quotes (malformed)", func(t *testing.T) {
		// This simulates the type of malformed CSV that causes "bare \" in non-quoted-field" errors
		csvData := `Retail Branding,Marketing Name,Device,Model
Samsung,Galaxy S21 "Pro",SM-G991B,SM-G991B
Google,Pixel 6 "Special Edition,oriole,Pixel 6
OnePlus,OnePlus 9 "T",LE2110,LE2110`

		deviceNames, err := parser.ParseData([]byte(csvData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 3) // All records should be parsed despite malformed quotes

		// Check that data is parsed correctly despite bare quotes
		assert.Equal(t, "Samsung", deviceNames[0].Brand)
		assert.Equal(t, "Galaxy S21 \"Pro\"", deviceNames[0].Name)
		assert.Equal(t, "Google", deviceNames[1].Brand)
		assert.Equal(t, "OnePlus", deviceNames[2].Brand)
	})

	t.Run("Real Google Play CSV format", func(t *testing.T) {
		// This simulates the actual format from Google Play CSV
		csvData := `Retail Branding,Marketing Name,Device,Model
"","","AD681H","Smartfren Andromax AD681H"
"","","FJL21","FJL21"
"1&1","1&1 Puck","diw362_1u1","DIW362P 1U1"
"Samsung","Galaxy S21","SM-G991B","SM-G991B"`

		deviceNames, err := parser.ParseData([]byte(csvData))
		require.NoError(t, err)
		// Only entries with non-empty required fields should be included
		assert.Len(t, deviceNames, 2) // Only 1&1 and Samsung entries should be included

		// Check that valid entries are parsed correctly
		assert.Equal(t, "1&1", deviceNames[0].Brand)
		assert.Equal(t, "1&1 Puck", deviceNames[0].Name)
		assert.Equal(t, "DIW362P 1U1", deviceNames[0].Model)

		assert.Equal(t, "Samsung", deviceNames[1].Brand)
		assert.Equal(t, "Galaxy S21", deviceNames[1].Name)
		assert.Equal(t, "SM-G991B", deviceNames[1].Model)
	})

	t.Run("CSV with UTF-8 BOM", func(t *testing.T) {
		// Test with UTF-8 BOM (Byte Order Mark) which is present in the actual Google Play CSV
		bomBytes := []byte{0xEF, 0xBB, 0xBF} // UTF-8 BOM
		csvContent := "Retail Branding,Marketing Name,Device,Model\n\"1&1\",\"1&1 Puck\",\"diw362_1u1\",\"DIW362P 1U1\""
		csvData := append(bomBytes, []byte(csvContent)...)

		deviceNames, err := parser.ParseData(csvData)
		require.NoError(t, err)
		assert.Len(t, deviceNames, 1)
		assert.Equal(t, "1&1", deviceNames[0].Brand)
		assert.Equal(t, "1&1 Puck", deviceNames[0].Name)
		assert.Equal(t, "DIW362P 1U1", deviceNames[0].Model)
	})

	t.Run("CSV with UTF-16 LE BOM", func(t *testing.T) {
		// Test with UTF-16 LE BOM which is actually present in the Google Play CSV
		csvContent := "Retail Branding,Marketing Name,Device,Model\n\"1&1\",\"1&1 Puck\",\"diw362_1u1\",\"DIW362P 1U1\""

		// Convert to UTF-16 LE with BOM
		runes := []rune(csvContent)
		u16s := utf16.Encode(runes)
		csvData := []byte{0xFF, 0xFE} // UTF-16 LE BOM
		for _, u16 := range u16s {
			csvData = append(csvData, byte(u16), byte(u16>>8)) // Little endian
		}

		deviceNames, err := parser.ParseData(csvData)
		require.NoError(t, err)
		assert.Len(t, deviceNames, 1)
		assert.Equal(t, "1&1", deviceNames[0].Brand)
		assert.Equal(t, "1&1 Puck", deviceNames[0].Name)
		assert.Equal(t, "DIW362P 1U1", deviceNames[0].Model)
	})

	//t.Run("Debug actual Google Play CSV", func(t *testing.T) {
	//	// Skip this test in CI/automated environments
	//	if testing.Short() {
	//		t.Skip("Skipping actual CSV download test in short mode")
	//	}
	//
	//	// Download the actual CSV file to debug the issue
	//	client := &http.Client{Timeout: 30 * time.Second}
	//	logger := zaptest.NewLogger(t)
	//
	//	data, err := DownloadData(context.Background(), &DefaultHTTPClient{client: client},
	//		"https://storage.googleapis.com/play_public/supported_devices.csv", logger)
	//	require.NoError(t, err)
	//
	//	// Examine the first 200 bytes to see what's actually in the file
	//	headerBytes := data
	//	if len(data) > 200 {
	//		headerBytes = data[:200]
	//	}
	//
	//	t.Logf("First 200 bytes of CSV (hex): %x", headerBytes)
	//	t.Logf("First 200 bytes of CSV (string): %q", string(headerBytes))
	//
	//	// Try to parse with our parser
	//	deviceNames, err := parser.ParseData(data)
	//	if err != nil {
	//		t.Logf("Parser error: %v", err)
	//
	//		// Let's examine just the header parsing
	//		lines := string(data[:500]) // First 500 bytes should contain the header
	//		t.Logf("First 500 chars: %q", lines)
	//	} else {
	//		t.Logf("Successfully parsed %d device names", len(deviceNames))
	//	}
	//})
}

func TestAndroidParser_findColumnIndices(t *testing.T) {
	parser := NewAndroidParser()

	t.Run("all columns present", func(t *testing.T) {
		header := []string{"Retail Branding", "Marketing Name", "Device", "Model"}
		indices, err := parser.findColumnIndices(header)
		require.NoError(t, err)
		assert.Equal(t, 0, indices.retailBranding)
		assert.Equal(t, 1, indices.marketingName)
		assert.Equal(t, 3, indices.model)
	})

	t.Run("missing retail branding column", func(t *testing.T) {
		header := []string{"Marketing Name", "Device", "Model"}
		indices, err := parser.findColumnIndices(header)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "missing required column: retail branding")
		assert.Nil(t, indices)
	})
}
